"use client";

import { useRef } from "react";
import { OrbitControls, Stage } from "@react-three/drei";
import { Monopente } from "./Monopente";

const Experience = () => {
    const monopenteRef = useRef(null);

    const handleChangeRoof = () => {
        monopenteRef.current?.changeRoofColor("red");
    };

    const handleHideBeam = () => {
        monopenteRef.current?.hideBeam();
    };

    const handleShowBeam = () => {
        monopenteRef.current?.showBeam();
    };

    return (
        <>
            <Stage
                intensity={1.5}
                environment="forest"
                adjustCamera={1}
                shadows={{
                    type: "accumulative",
                    colorBlend: 5,
                    color: "#ffffff",
                }}
            >
                <Monopente ref={monopenteRef} />
            </Stage>

            <OrbitControls
                makeDefault
                minDistance={8}
                maxDistance={50}
                minPolarAngle={0}
                maxPolarAngle={Math.PI / 2}
            />

            {/* Simple buttons to test your functions */}
            <div className="absolute top-4 left-4 space-x-2">
                <button
                    onClick={handleChangeRoof}
                    className="px-2 py-1 bg-red-500 text-white rounded"
                >
                    Red Roof
                </button>
                <button
                    onClick={handleHideBeam}
                    className="px-2 py-1 bg-gray-500 text-white rounded"
                >
                    Hide Beam
                </button>
                <button
                    onClick={handleShowBeam}
                    className="px-2 py-1 bg-green-500 text-white rounded"
                >
                    Show Beam
                </button>
            </div>
        </>
    );
};

export default Experience;

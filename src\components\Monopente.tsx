/* eslint-disable react/no-unknown-property */
/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Command: npx gltfjsx@6.5.0 public/models/monopente.gltf 
*/

import { useGLTF } from "@react-three/drei";
import { useImperativeHandle, useRef } from "react";

export interface MonopenteRef {
    changeRoofColor: (color: string) => void;
    changeWallColor: (color: string) => void;
}

const Monopente = forwardRef<MonopenteRef>((props, ref) => {
    const { nodes, materials } = useGLTF("./models/monopente.gltf");
    const panelRef1 = useRef();
    const panelRef2 = useRef();
    const panelRef3 = useRef();
    const panelRef4 = useRef();

    useImperativeHandle(ref, () => ({
        changeRoofColor: (color) => {
            roofRef.current.material.color.set(color);
        },
        hideBeam: () => {
            beamRef.current.visible = false;
        },
        showBeam: () => {
            beamRef.current.visible = true;
        },
    }));
    return (
        <group {...props} dispose={null}>
            <mesh
                castShadow
                geometry={nodes.Plane.geometry}
                material={materials["Material.001"]}
                position={[-7.999, 5.502, -2.226]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane002.geometry}
                material={materials["Material.001"]}
                position={[-3.303, 7.51, -2.231]}
                rotation={[Math.PI / 2, -0.175, Math.PI / 2]}
                scale={[0.1, 1.724, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane003.geometry}
                material={materials["Material.001"]}
                position={[-7.796, 8.111, -2.226]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.388]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane001.geometry}
                material={materials["Material.001"]}
                position={[1.384, 4.696, -2.236]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 0.806, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane004.geometry}
                material={materials["Material.001"]}
                position={[1.184, 6.521, -2.236]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.395]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane005.geometry}
                material={materials["Material.001"]}
                position={[-7.512, 7.9, -2.227]}
                rotation={[-Math.PI / 2, -Math.PI / 9, -Math.PI / 2]}
                scale={[0.1, 0.368, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane006.geometry}
                material={materials["Material.001"]}
                position={[0.638, 6.457, -2.235]}
                rotation={[Math.PI / 2, -0.436, Math.PI / 2]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane007.geometry}
                material={materials["Material.001"]}
                position={[-7.999, 2.501, -2.226]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane008.geometry}
                material={materials["Material.001"]}
                position={[1.385, 2.505, -2.236]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane009.geometry}
                material={materials["Material.001"]}
                position={[-8.042, 5.461, -5.207]}
                rotation={[0, -1.57, -2.331]}
                scale={[0.047, 0.161, 0.047]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane010.geometry}
                material={materials["Material.001"]}
                position={[-8.006, 5.502, -8.208]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane011.geometry}
                material={materials["Material.001"]}
                position={[-3.31, 7.51, -8.213]}
                rotation={[Math.PI / 2, -0.175, Math.PI / 2]}
                scale={[0.1, 1.724, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane012.geometry}
                material={materials["Material.001"]}
                position={[-7.803, 8.111, -8.208]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.388]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane013.geometry}
                material={materials["Material.001"]}
                position={[1.378, 4.696, -8.218]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 0.806, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane014.geometry}
                material={materials["Material.001"]}
                position={[1.177, 6.521, -8.218]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.395]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane015.geometry}
                material={materials["Material.001"]}
                position={[-7.518, 7.9, -8.208]}
                rotation={[-Math.PI / 2, -Math.PI / 9, -Math.PI / 2]}
                scale={[0.1, 0.368, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane016.geometry}
                material={materials["Material.001"]}
                position={[0.632, 6.457, -8.218]}
                rotation={[Math.PI / 2, -0.436, Math.PI / 2]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane017.geometry}
                material={materials["Material.001"]}
                position={[-8.006, 2.501, -8.208]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane018.geometry}
                material={materials["Material.001"]}
                position={[1.379, 2.505, -8.218]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane019.geometry}
                material={materials["Material.001"]}
                position={[-8.088, 5.461, -5.209]}
                rotation={[0, -1.57, 2.33]}
                scale={[-0.047, -0.161, -0.047]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane020.geometry}
                material={materials["Material.001"]}
                position={[1.394, 4.676, -5.219]}
                rotation={[-0.986, 0.001, 0.001]}
                scale={[-0.041, -0.14, -0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane021.geometry}
                material={materials["Material.001"]}
                position={[1.348, 4.676, -5.218]}
                rotation={[0.986, 0.001, -0.001]}
                scale={[0.041, 0.14, 0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane022.geometry}
                material={materials["Material.001"]}
                position={[-5.308, 7.907, -5.235]}
                rotation={[-1.587, 0.167, 2.477]}
                scale={[0.043, 0.147, 0.043]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane023.geometry}
                material={materials["Material.001"]}
                position={[-5.314, 7.95, -5.197]}
                rotation={[-1.569, 0.185, 0.669]}
                scale={[-0.044, -0.148, -0.044]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane024.geometry}
                material={materials["Material.001"]}
                position={[-0.99, 7.147, -5.245]}
                rotation={[-1.571, 0.197, -0.589]}
                scale={[-0.041, -0.14, -0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane025.geometry}
                material={materials["Material.001"]}
                position={[-0.995, 7.106, -5.191]}
                rotation={[-1.56, 0.179, -2.547]}
                scale={[0.041, 0.14, 0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane026.geometry}
                material={materials["Material.002"]}
                position={[-3.239, 7.991, 1.288]}
                rotation={[Math.PI / 2, 0, 0]}
            />
            {/* Panel block 2 */}
            <group
                ref={panelRef2}
                position={[-8.196, 8.684, -5.223]}
                rotation={[Math.PI / 2, -0.175, 0]}
                scale={[10.028, 3.099, 3.526]}
            >
                <mesh
                    castShadow
                    geometry={nodes.Cube014.geometry}
                    material={materials["Black_Metal_Mat.001"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube014_1.geometry}
                    material={materials["White_Lines.002"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube014_2.geometry}
                    material={materials["Metal.001"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube014_3.geometry}
                    material={materials["Glass.001"]}
                />
            </group>
            <mesh
                castShadow
                geometry={nodes.Plane027.geometry}
                material={materials["Material.003"]}
                position={[-3.133, 2.504, -5.221]}
                rotation={[Math.PI / 2, 0, 0]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane028.geometry}
                material={materials["Material.001"]}
                position={[-7.999, 5.502, 11.073]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane029.geometry}
                material={materials["Material.001"]}
                position={[-3.303, 7.51, 11.068]}
                rotation={[Math.PI / 2, -0.175, Math.PI / 2]}
                scale={[0.1, 1.724, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane030.geometry}
                material={materials["Material.001"]}
                position={[-7.796, 8.111, 11.073]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.388]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane031.geometry}
                material={materials["Material.001"]}
                position={[1.384, 4.696, 11.063]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 0.806, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane032.geometry}
                material={materials["Material.001"]}
                position={[1.184, 6.521, 11.063]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.395]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane033.geometry}
                material={materials["Material.001"]}
                position={[-7.512, 7.9, 11.073]}
                rotation={[-Math.PI / 2, -Math.PI / 9, -Math.PI / 2]}
                scale={[0.1, 0.368, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane034.geometry}
                material={materials["Material.001"]}
                position={[0.638, 6.457, 11.064]}
                rotation={[Math.PI / 2, -0.436, Math.PI / 2]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane035.geometry}
                material={materials["Material.001"]}
                position={[-7.999, 2.501, 11.073]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane036.geometry}
                material={materials["Material.001"]}
                position={[1.385, 2.505, 11.063]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane037.geometry}
                material={materials["Material.001"]}
                position={[-8.042, 5.461, 8.092]}
                rotation={[0, -1.57, -2.331]}
                scale={[0.047, 0.161, 0.047]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane038.geometry}
                material={materials["Material.001"]}
                position={[-8.006, 5.502, 5.091]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane039.geometry}
                material={materials["Material.001"]}
                position={[-3.31, 7.51, 5.086]}
                rotation={[Math.PI / 2, -0.175, Math.PI / 2]}
                scale={[0.1, 1.724, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane040.geometry}
                material={materials["Material.001"]}
                position={[-7.803, 8.111, 5.091]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.388]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane041.geometry}
                material={materials["Material.001"]}
                position={[1.378, 4.696, 5.081]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 0.806, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane042.geometry}
                material={materials["Material.001"]}
                position={[1.177, 6.521, 5.081]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.395]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane043.geometry}
                material={materials["Material.001"]}
                position={[-7.518, 7.9, 5.091]}
                rotation={[-Math.PI / 2, -Math.PI / 9, -Math.PI / 2]}
                scale={[0.1, 0.368, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane044.geometry}
                material={materials["Material.001"]}
                position={[0.632, 6.457, 5.082]}
                rotation={[Math.PI / 2, -0.436, Math.PI / 2]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane045.geometry}
                material={materials["Material.001"]}
                position={[-8.006, 2.501, 5.091]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane046.geometry}
                material={materials["Material.001"]}
                position={[1.379, 2.505, 5.081]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane047.geometry}
                material={materials["Material.001"]}
                position={[-8.088, 5.461, 8.09]}
                rotation={[0, -1.57, 2.33]}
                scale={[-0.047, -0.161, -0.047]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane048.geometry}
                material={materials["Material.001"]}
                position={[1.394, 4.676, 8.081]}
                rotation={[-0.986, 0.001, 0.001]}
                scale={[-0.041, -0.14, -0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane049.geometry}
                material={materials["Material.001"]}
                position={[1.348, 4.676, 8.081]}
                rotation={[0.986, 0.001, -0.001]}
                scale={[0.041, 0.14, 0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane050.geometry}
                material={materials["Material.001"]}
                position={[-5.308, 7.907, 8.064]}
                rotation={[-1.587, 0.167, 2.477]}
                scale={[0.043, 0.147, 0.043]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane051.geometry}
                material={materials["Material.001"]}
                position={[-5.314, 7.95, 8.102]}
                rotation={[-1.569, 0.185, 0.669]}
                scale={[-0.044, -0.148, -0.044]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane052.geometry}
                material={materials["Material.001"]}
                position={[-0.99, 7.147, 8.054]}
                rotation={[-1.571, 0.197, -0.589]}
                scale={[-0.041, -0.14, -0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane053.geometry}
                material={materials["Material.001"]}
                position={[-0.995, 7.106, 8.108]}
                rotation={[-1.56, 0.179, -2.547]}
                scale={[0.041, 0.14, 0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane054.geometry}
                material={materials["Material.002"]}
                position={[-3.239, 7.991, 14.587]}
                rotation={[Math.PI / 2, 0, 0]}
            />
            {/* Panel block 1 */}
            <group
                ref={panelRef1}
                position={[-8.196, 8.684, 8.076]}
                rotation={[Math.PI / 2, -0.175, 0]}
                scale={[10.028, 3.099, 3.526]}
            >
                <mesh
                    castShadow
                    geometry={nodes.Cube001_1.geometry}
                    material={materials["Black_Metal_Mat.001"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube001_2.geometry}
                    material={materials["White_Lines.002"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube001_3.geometry}
                    material={materials["Metal.001"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube001_4.geometry}
                    material={materials["Glass.001"]}
                />
            </group>
            <mesh
                castShadow
                geometry={nodes.Plane055.geometry}
                material={materials["Material.003"]}
                position={[-3.133, 2.504, 8.078]}
                rotation={[Math.PI / 2, 0, 0]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane056.geometry}
                material={materials["Material.001"]}
                position={[-7.999, 5.502, -28.857]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane057.geometry}
                material={materials["Material.001"]}
                position={[-3.303, 7.51, -28.862]}
                rotation={[Math.PI / 2, -0.175, Math.PI / 2]}
                scale={[0.1, 1.724, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane058.geometry}
                material={materials["Material.001"]}
                position={[-7.796, 8.111, -28.857]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.388]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane059.geometry}
                material={materials["Material.001"]}
                position={[1.384, 4.696, -28.868]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 0.806, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane060.geometry}
                material={materials["Material.001"]}
                position={[1.184, 6.521, -28.868]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.395]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane061.geometry}
                material={materials["Material.001"]}
                position={[-7.512, 7.9, -28.858]}
                rotation={[-Math.PI / 2, -Math.PI / 9, -Math.PI / 2]}
                scale={[0.1, 0.368, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane062.geometry}
                material={materials["Material.001"]}
                position={[0.638, 6.457, -28.867]}
                rotation={[Math.PI / 2, -0.436, Math.PI / 2]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane063.geometry}
                material={materials["Material.001"]}
                position={[-7.999, 2.501, -28.857]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane064.geometry}
                material={materials["Material.001"]}
                position={[1.385, 2.505, -28.868]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane065.geometry}
                material={materials["Material.001"]}
                position={[-8.042, 5.461, -31.838]}
                rotation={[0, -1.57, -2.331]}
                scale={[0.047, 0.161, 0.047]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane066.geometry}
                material={materials["Material.001"]}
                position={[-8.006, 5.502, -34.839]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane067.geometry}
                material={materials["Material.001"]}
                position={[-3.31, 7.51, -34.845]}
                rotation={[Math.PI / 2, -0.175, Math.PI / 2]}
                scale={[0.1, 1.724, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane068.geometry}
                material={materials["Material.001"]}
                position={[-7.803, 8.111, -34.839]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.388]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane069.geometry}
                material={materials["Material.001"]}
                position={[1.378, 4.696, -34.85]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 0.806, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane070.geometry}
                material={materials["Material.001"]}
                position={[1.177, 6.521, -34.85]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.395]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane071.geometry}
                material={materials["Material.001"]}
                position={[-7.518, 7.9, -34.839]}
                rotation={[-Math.PI / 2, -Math.PI / 9, -Math.PI / 2]}
                scale={[0.1, 0.368, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane072.geometry}
                material={materials["Material.001"]}
                position={[0.632, 6.457, -34.849]}
                rotation={[Math.PI / 2, -0.436, Math.PI / 2]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane073.geometry}
                material={materials["Material.001"]}
                position={[-8.006, 2.501, -34.839]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane074.geometry}
                material={materials["Material.001"]}
                position={[1.379, 2.505, -34.85]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane075.geometry}
                material={materials["Material.001"]}
                position={[-8.088, 5.461, -31.84]}
                rotation={[0, -1.57, 2.33]}
                scale={[-0.047, -0.161, -0.047]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane076.geometry}
                material={materials["Material.001"]}
                position={[1.394, 4.676, -31.85]}
                rotation={[-0.986, 0.001, 0.001]}
                scale={[-0.041, -0.14, -0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane077.geometry}
                material={materials["Material.001"]}
                position={[1.348, 4.676, -31.85]}
                rotation={[0.986, 0.001, -0.001]}
                scale={[0.041, 0.14, 0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane078.geometry}
                material={materials["Material.001"]}
                position={[-5.308, 7.907, -31.867]}
                rotation={[-1.587, 0.167, 2.477]}
                scale={[0.043, 0.147, 0.043]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane079.geometry}
                material={materials["Material.001"]}
                position={[-5.314, 7.95, -31.828]}
                rotation={[-1.569, 0.185, 0.669]}
                scale={[-0.044, -0.148, -0.044]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane080.geometry}
                material={materials["Material.001"]}
                position={[-0.99, 7.147, -31.876]}
                rotation={[-1.571, 0.197, -0.589]}
                scale={[-0.041, -0.14, -0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane081.geometry}
                material={materials["Material.001"]}
                position={[-0.995, 7.106, -31.822]}
                rotation={[-1.56, 0.179, -2.547]}
                scale={[0.041, 0.14, 0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane082.geometry}
                material={materials["Material.002"]}
                position={[-3.239, 7.991, -25.344]}
                rotation={[Math.PI / 2, 0, 0]}
            />
            {/* Panel block 4 */}
            <group
                ref={panelRef4}
                position={[-8.196, 8.684, -31.855]}
                rotation={[Math.PI / 2, -0.175, 0]}
                scale={[10.028, 3.099, 3.526]}
            >
                <mesh
                    castShadow
                    geometry={nodes.Cube002_1.geometry}
                    material={materials["Black_Metal_Mat.001"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube002_2.geometry}
                    material={materials["White_Lines.002"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube002_3.geometry}
                    material={materials["Metal.001"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube002_4.geometry}
                    material={materials["Glass.001"]}
                />
            </group>
            <mesh
                castShadow
                geometry={nodes.Plane083.geometry}
                material={materials["Material.003"]}
                position={[-3.133, 2.504, -31.853]}
                rotation={[Math.PI / 2, 0, 0]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane084.geometry}
                material={materials["Material.001"]}
                position={[-7.999, 5.502, -15.558]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane085.geometry}
                material={materials["Material.001"]}
                position={[-3.303, 7.51, -15.563]}
                rotation={[Math.PI / 2, -0.175, Math.PI / 2]}
                scale={[0.1, 1.724, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane086.geometry}
                material={materials["Material.001"]}
                position={[-7.796, 8.111, -15.558]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.388]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane087.geometry}
                material={materials["Material.001"]}
                position={[1.384, 4.696, -15.569]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 0.806, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane088.geometry}
                material={materials["Material.001"]}
                position={[1.184, 6.521, -15.568]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.395]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane089.geometry}
                material={materials["Material.001"]}
                position={[-7.512, 7.9, -15.559]}
                rotation={[-Math.PI / 2, -Math.PI / 9, -Math.PI / 2]}
                scale={[0.1, 0.368, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane090.geometry}
                material={materials["Material.001"]}
                position={[0.638, 6.457, -15.568]}
                rotation={[Math.PI / 2, -0.436, Math.PI / 2]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane091.geometry}
                material={materials["Material.001"]}
                position={[-7.999, 2.501, -15.558]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane092.geometry}
                material={materials["Material.001"]}
                position={[1.385, 2.505, -15.569]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane093.geometry}
                material={materials["Material.001"]}
                position={[-8.042, 5.461, -18.539]}
                rotation={[0, -1.57, -2.331]}
                scale={[0.047, 0.161, 0.047]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane094.geometry}
                material={materials["Material.001"]}
                position={[-8.006, 5.502, -21.54]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane095.geometry}
                material={materials["Material.001"]}
                position={[-3.31, 7.51, -21.546]}
                rotation={[Math.PI / 2, -0.175, Math.PI / 2]}
                scale={[0.1, 1.724, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane096.geometry}
                material={materials["Material.001"]}
                position={[-7.803, 8.111, -21.54]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.388]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane097.geometry}
                material={materials["Material.001"]}
                position={[1.378, 4.696, -21.551]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 0.806, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane098.geometry}
                material={materials["Material.001"]}
                position={[1.177, 6.521, -21.551]}
                rotation={[Math.PI / 2, 0, 1.57]}
                scale={[0.1, 0.362, 0.395]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane099.geometry}
                material={materials["Material.001"]}
                position={[-7.518, 7.9, -21.54]}
                rotation={[-Math.PI / 2, -Math.PI / 9, -Math.PI / 2]}
                scale={[0.1, 0.368, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane100.geometry}
                material={materials["Material.001"]}
                position={[0.632, 6.457, -21.55]}
                rotation={[Math.PI / 2, -0.436, Math.PI / 2]}
                scale={[0.1, 1, 0.2]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane101.geometry}
                material={materials["Material.001"]}
                position={[-8.006, 2.501, -21.54]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane102.geometry}
                material={materials["Material.001"]}
                position={[1.379, 2.505, -21.551]}
                rotation={[0, -1.571, 0]}
                scale={[0.1, 4.238, 0.21]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane103.geometry}
                material={materials["Material.001"]}
                position={[-8.088, 5.461, -18.541]}
                rotation={[0, -1.57, 2.33]}
                scale={[-0.047, -0.161, -0.047]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane104.geometry}
                material={materials["Material.001"]}
                position={[1.394, 4.676, -18.551]}
                rotation={[-0.986, 0.001, 0.001]}
                scale={[-0.041, -0.14, -0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane105.geometry}
                material={materials["Material.001"]}
                position={[1.348, 4.676, -18.551]}
                rotation={[0.986, 0.001, -0.001]}
                scale={[0.041, 0.14, 0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane106.geometry}
                material={materials["Material.001"]}
                position={[-5.308, 7.907, -18.568]}
                rotation={[-1.587, 0.167, 2.477]}
                scale={[0.043, 0.147, 0.043]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane107.geometry}
                material={materials["Material.001"]}
                position={[-5.314, 7.95, -18.529]}
                rotation={[-1.569, 0.185, 0.669]}
                scale={[-0.044, -0.148, -0.044]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane108.geometry}
                material={materials["Material.001"]}
                position={[-0.99, 7.147, -18.577]}
                rotation={[-1.571, 0.197, -0.589]}
                scale={[-0.041, -0.14, -0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane109.geometry}
                material={materials["Material.001"]}
                position={[-0.995, 7.106, -18.523]}
                rotation={[-1.56, 0.179, -2.547]}
                scale={[0.041, 0.14, 0.041]}
            />
            <mesh
                castShadow
                geometry={nodes.Plane110.geometry}
                material={materials["Material.002"]}
                position={[-3.239, 7.991, -12.045]}
                rotation={[Math.PI / 2, 0, 0]}
            />
            {/* Panel block 3 */}
            <group
                ref={panelRef3}
                position={[-8.196, 8.684, -18.556]}
                rotation={[Math.PI / 2, -0.175, 0]}
                scale={[10.028, 3.099, 3.526]}
            >
                <mesh
                    castShadow
                    geometry={nodes.Cube003_1.geometry}
                    material={materials["Black_Metal_Mat.001"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube003_2.geometry}
                    material={materials["White_Lines.002"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube003_3.geometry}
                    material={materials["Metal.001"]}
                />
                <mesh
                    castShadow
                    geometry={nodes.Cube003_4.geometry}
                    material={materials["Glass.001"]}
                />
            </group>
            <mesh
                castShadow
                geometry={nodes.Plane111.geometry}
                material={materials["Material.003"]}
                position={[-3.133, 2.504, -18.554]}
                rotation={[Math.PI / 2, 0, 0]}
            />
        </group>
    );
}

useGLTF.preload("./models/monopente.gltf");
